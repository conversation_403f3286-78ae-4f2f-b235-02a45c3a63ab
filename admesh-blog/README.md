# AdMesh Blog

A standalone Next.js blog application for AdMesh, powered by Sanity CMS.

## Features

- 📝 Blog posts with rich content editing via Sanity CMS
- 👥 Author profiles and management
- 🏷️ Categories and tags
- 📱 Fully responsive design
- 🎨 Dark/light theme support
- 🔍 SEO optimized
- ⚡ Fast static generation with ISR
- 📊 Analytics with Vercel Analytics

## Tech Stack

- **Framework**: Next.js 15
- **CMS**: Sanity
- **Styling**: Tailwind CSS
- **UI Components**: shadcn/ui
- **Deployment**: Vercel
- **Analytics**: Vercel Analytics & Speed Insights

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn
- Sanity account and project

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd admesh-blog
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
```

Fill in your Sanity project details:
```env
NEXT_PUBLIC_SANITY_PROJECT_ID=h0kukqbi
NEXT_PUBLIC_SANITY_DATASET=production
NEXT_PUBLIC_SANITY_API_VERSION=2024-01-01
SANITY_API_TOKEN=your_sanity_api_token_here
NEXT_PUBLIC_ENVIRONMENT=development
NEXT_PUBLIC_BLOG_URL=http://localhost:3001
NEXT_PUBLIC_MAIN_SITE_URL=https://useadmesh.com
```

4. Run the development server:
```bash
npm run dev
```

The blog will be available at [http://localhost:3001](http://localhost:3001).

## Content Management

Content is managed through Sanity CMS. The blog supports:

- **Blog Posts**: Rich text content with images, code blocks, and embedded media
- **Authors**: Author profiles with bio, social links, and profile images
- **Categories**: Organized content categorization with color coding
- **SEO**: Custom meta titles, descriptions, and Open Graph images

### Sanity Studio

To manage content, you'll need access to the Sanity Studio. The studio is configured to work with the existing AdMesh Sanity project.

## Deployment

### Vercel (Recommended)

1. Connect your repository to Vercel
2. Set the following environment variables in Vercel:
   - `NEXT_PUBLIC_SANITY_PROJECT_ID`
   - `NEXT_PUBLIC_SANITY_DATASET`
   - `NEXT_PUBLIC_SANITY_API_VERSION`
   - `SANITY_API_TOKEN`
   - `NEXT_PUBLIC_ENVIRONMENT=production`
   - `NEXT_PUBLIC_BLOG_URL=https://blog.useadmesh.com`
   - `NEXT_PUBLIC_MAIN_SITE_URL=https://useadmesh.com`

3. Deploy!

### Custom Domain

To use a custom domain like `blog.useadmesh.com`:

1. Add the domain in Vercel dashboard
2. Update DNS records to point to Vercel
3. Update environment variables accordingly

## Project Structure

```
admesh-blog/
├── src/
│   ├── app/                 # Next.js app directory
│   │   ├── globals.css     # Global styles
│   │   ├── layout.tsx      # Root layout
│   │   ├── page.tsx        # Blog listing page
│   │   └── posts/          # Individual blog posts
│   ├── components/         # React components
│   │   └── ui/            # shadcn/ui components
│   ├── lib/               # Utility functions
│   │   ├── sanity.ts      # Sanity client and queries
│   │   └── utils.ts       # General utilities
│   └── sanity/            # Sanity configuration
│       ├── config.ts      # Sanity config
│       └── schemas/       # Content schemas
├── public/                # Static assets
├── package.json
├── tailwind.config.mjs
├── next.config.mjs
└── vercel.json           # Vercel deployment config
```

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript type checking

### Adding New Content Types

To add new content types:

1. Create schema in `src/sanity/schemas/`
2. Add to `src/sanity/schemas/index.ts`
3. Update TypeScript interfaces in `src/lib/sanity.ts`
4. Add queries and helper functions
5. Create corresponding pages/components

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is proprietary to AdMesh.
