{"name": "admesh-blog", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 3001", "dev:test": "NEXT_PUBLIC_ENVIRONMENT=test next dev --port 3001", "dev:prod": "NEXT_PUBLIC_ENVIRONMENT=production next dev --port 3001", "build": "next build", "build:test": "NEXT_PUBLIC_ENVIRONMENT=test next build", "build:prod": "NEXT_PUBLIC_ENVIRONMENT=production next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@portabletext/react": "^3.2.1", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tooltip": "^1.1.8", "@sanity/client": "^7.6.0", "@sanity/image-url": "^1.0.2", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.6.3", "lucide-react": "^0.487.0", "next": "15.2.4", "next-sanity": "^9.12.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.1.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.2.4", "postcss": "^8.5.3", "tailwindcss": "^4.1.1", "typescript": "^5"}}