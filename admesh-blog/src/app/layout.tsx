import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "next-themes";
import { Analytics } from "@vercel/analytics/react";
import { SpeedInsights } from "@vercel/speed-insights/next";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: {
    default: "AdMesh Blog",
    template: "%s | AdMesh Blog",
  },
  description: "Insights, tutorials, and updates about AI-powered recommendation systems, monetization strategies, and the future of digital commerce.",
  keywords: ["AI", "recommendations", "monetization", "digital commerce", "AdMesh", "blog"],
  authors: [{ name: "AdMesh Team" }],
  creator: "<PERSON><PERSON><PERSON>",
  publisher: "AdMesh",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: process.env.NEXT_PUBLIC_BLOG_URL || "https://blog.useadmesh.com",
    siteName: "AdMesh Blog",
    title: "AdMesh Blog",
    description: "Insights, tutorials, and updates about AI-powered recommendation systems, monetization strategies, and the future of digital commerce.",
  },
  twitter: {
    card: "summary_large_image",
    title: "AdMesh Blog",
    description: "Insights, tutorials, and updates about AI-powered recommendation systems, monetization strategies, and the future of digital commerce.",
    creator: "@useadmesh",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code",
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <div className="min-h-screen bg-background">
            <header className="border-b">
              <div className="container mx-auto px-4 py-4">
                <nav className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <a href={process.env.NEXT_PUBLIC_MAIN_SITE_URL || "https://useadmesh.com"} className="text-xl font-bold">
                      AdMesh
                    </a>
                    <span className="text-muted-foreground">/</span>
                    <a href="/" className="text-lg font-medium">
                      Blog
                    </a>
                  </div>
                  <div className="flex items-center space-x-4">
                    <a 
                      href={process.env.NEXT_PUBLIC_MAIN_SITE_URL || "https://useadmesh.com"} 
                      className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                    >
                      Back to AdMesh
                    </a>
                  </div>
                </nav>
              </div>
            </header>
            <main>{children}</main>
            <footer className="border-t mt-16">
              <div className="container mx-auto px-4 py-8">
                <div className="flex flex-col md:flex-row justify-between items-center">
                  <div className="text-sm text-muted-foreground">
                    © 2024 AdMesh. All rights reserved.
                  </div>
                  <div className="flex items-center space-x-4 mt-4 md:mt-0">
                    <a 
                      href={`${process.env.NEXT_PUBLIC_MAIN_SITE_URL || "https://useadmesh.com"}/privacy`}
                      className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                    >
                      Privacy
                    </a>
                    <a 
                      href={`${process.env.NEXT_PUBLIC_MAIN_SITE_URL || "https://useadmesh.com"}/terms`}
                      className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                    >
                      Terms
                    </a>
                  </div>
                </div>
              </div>
            </footer>
          </div>
        </ThemeProvider>
        <Analytics />
        <SpeedInsights />
      </body>
    </html>
  );
}
