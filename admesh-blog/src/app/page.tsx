import { Metadata } from 'next'
import Link from 'next/link'
import Image from 'next/image'
import { getBlogPosts, getFeaturedBlogPosts, urlFor, type BlogPost } from '@/lib/sanity'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { CalendarDays, User } from 'lucide-react'

export const metadata: Metadata = {
  title: 'AdMesh Blog - AI-Powered Recommendation Insights',
  description: 'Latest insights, tutorials, and updates from the AdMesh team about AI-powered recommendation systems and monetization.',
}

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  })
}

function BlogPostCard({ post }: { post: BlogPost }) {
  return (
    <Card className="h-full hover:shadow-lg transition-shadow">
      <Link href={`/posts/${post.slug.current}`}>
        {post.mainImage && (
          <div className="relative h-48 w-full overflow-hidden rounded-t-lg">
            <Image
              src={urlFor(post.mainImage).width(400).height(200).url()}
              alt={post.mainImage.alt || post.title}
              fill
              className="object-cover transition-transform hover:scale-105"
            />
          </div>
        )}
        <CardHeader>
          <div className="flex flex-wrap gap-2 mb-2">
            {post.categories?.map((category) => (
              <Badge
                key={category._id}
                variant="secondary"
                className={`bg-${category.color}-100 text-${category.color}-800`}
              >
                {category.title}
              </Badge>
            ))}
          </div>
          <CardTitle className="line-clamp-2 hover:text-primary transition-colors">
            {post.title}
          </CardTitle>
          <CardDescription className="line-clamp-3">
            {post.excerpt}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <User className="h-4 w-4" />
              <span>{post.author.name}</span>
            </div>
            <div className="flex items-center gap-2">
              <CalendarDays className="h-4 w-4" />
              <span>{formatDate(post.publishedAt)}</span>
            </div>
          </div>
        </CardContent>
      </Link>
    </Card>
  )
}

function FeaturedPostCard({ post }: { post: BlogPost }) {
  return (
    <Card className="col-span-full lg:col-span-2 hover:shadow-lg transition-shadow">
      <Link href={`/posts/${post.slug.current}`}>
        <div className="grid md:grid-cols-2 gap-6">
          {post.mainImage && (
            <div className="relative h-64 md:h-full overflow-hidden rounded-l-lg">
              <Image
                src={urlFor(post.mainImage).width(600).height(400).url()}
                alt={post.mainImage.alt || post.title}
                fill
                className="object-cover transition-transform hover:scale-105"
              />
            </div>
          )}
          <div className="p-6">
            <div className="flex flex-wrap gap-2 mb-4">
              <Badge variant="default" className="bg-primary">
                Featured
              </Badge>
              {post.categories?.map((category) => (
                <Badge
                  key={category._id}
                  variant="secondary"
                  className={`bg-${category.color}-100 text-${category.color}-800`}
                >
                  {category.title}
                </Badge>
              ))}
            </div>
            <h2 className="text-2xl font-bold mb-3 hover:text-primary transition-colors">
              {post.title}
            </h2>
            <p className="text-muted-foreground mb-4 line-clamp-3">
              {post.excerpt}
            </p>
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4" />
                <span>{post.author.name}</span>
                <span>•</span>
                <span className="capitalize">{post.author.role}</span>
              </div>
              <div className="flex items-center gap-2">
                <CalendarDays className="h-4 w-4" />
                <span>{formatDate(post.publishedAt)}</span>
              </div>
            </div>
          </div>
        </div>
      </Link>
    </Card>
  )
}

export default async function BlogPage() {
  try {
    const [featuredPosts, allPosts] = await Promise.all([
      getFeaturedBlogPosts(),
      getBlogPosts(),
    ])

    const regularPosts = allPosts.filter(post => !post.featured)

    return (
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4">AdMesh Blog</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Insights, tutorials, and updates about AI-powered recommendation systems, 
            monetization strategies, and the future of digital commerce.
          </p>
        </div>

        {/* Featured Posts */}
        {featuredPosts.length > 0 && (
          <section className="mb-12">
            <h2 className="text-2xl font-bold mb-6">Featured Posts</h2>
            <div className="grid gap-6">
              {featuredPosts.map((post) => (
                <FeaturedPostCard key={post._id} post={post} />
              ))}
            </div>
          </section>
        )}

        {/* All Posts */}
        <section>
          <h2 className="text-2xl font-bold mb-6">
            {featuredPosts.length > 0 ? 'Latest Posts' : 'All Posts'}
          </h2>
          {regularPosts.length > 0 ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {regularPosts.map((post) => (
                <BlogPostCard key={post._id} post={post} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <h3 className="text-xl font-semibold mb-2">No blog posts yet</h3>
              <p className="text-muted-foreground mb-6">
                We're working on creating amazing content for you. Check back soon!
              </p>
              <div className="bg-muted p-6 rounded-lg max-w-md mx-auto">
                <h4 className="font-semibold mb-2">Want to contribute?</h4>
                <p className="text-sm text-muted-foreground">
                  Content is managed through our Sanity CMS. 
                  Visit the Sanity Studio to create and publish blog posts.
                </p>
              </div>
            </div>
          )}
        </section>
      </div>
    )
  } catch (error) {
    console.error('Error fetching blog posts:', error)
    
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-4">AdMesh Blog</h1>
          <div className="bg-muted p-6 rounded-lg max-w-md mx-auto">
            <h3 className="text-xl font-semibold mb-2">Content Loading</h3>
            <p className="text-muted-foreground mb-4">
              We're setting up the blog content management system.
            </p>
            <p className="text-sm text-muted-foreground">
              Blog posts will be managed through Sanity CMS. 
              Make sure the Sanity Studio is running and content is published.
            </p>
          </div>
        </div>
      </div>
    )
  }
}
