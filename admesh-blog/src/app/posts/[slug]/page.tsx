import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'
import { getBlogPost, getBlogPosts, urlFor } from '@/lib/sanity'
import { PortableText } from '@portabletext/react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { CalendarDays, User, ArrowLeft, Clock } from 'lucide-react'

interface BlogPostPageProps {
  params: {
    slug: string
  }
}

// Generate static params for all blog posts
export async function generateStaticParams() {
  try {
    const posts = await getBlogPosts()
    return posts.map((post) => ({
      slug: post.slug.current,
    }))
  } catch (error) {
    console.error('Error generating static params:', error)
    return []
  }
}

// Generate metadata for each blog post
export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  try {
    const post = await getBlogPost(params.slug)
    
    if (!post) {
      return {
        title: 'Post Not Found | AdMesh Blog',
        description: 'The requested blog post could not be found.',
      }
    }

    const ogImage = post.seo?.ogImage || post.mainImage
    
    return {
      title: post.seo?.metaTitle || `${post.title} | AdMesh Blog`,
      description: post.seo?.metaDescription || post.excerpt,
      keywords: post.tags,
      authors: [{ name: post.author.name }],
      openGraph: {
        title: post.seo?.ogTitle || post.title,
        description: post.seo?.ogDescription || post.excerpt,
        type: 'article',
        publishedTime: post.publishedAt,
        authors: [post.author.name],
        images: ogImage ? [urlFor(ogImage).width(1200).height(630).url()] : [],
      },
      twitter: {
        card: 'summary_large_image',
        title: post.seo?.ogTitle || post.title,
        description: post.seo?.ogDescription || post.excerpt,
        images: ogImage ? [urlFor(ogImage).width(1200).height(630).url()] : [],
      },
    }
  } catch (error) {
    console.error('Error generating metadata:', error)
    return {
      title: 'Blog Post | AdMesh',
      description: 'Read the latest insights from the AdMesh team.',
    }
  }
}

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  })
}

function estimateReadingTime(body: any[]): number {
  if (!body) return 1
  
  const wordsPerMinute = 200
  const textContent = body
    .filter(block => block._type === 'block')
    .map(block => 
      block.children
        ?.filter((child: any) => child._type === 'span')
        ?.map((span: any) => span.text)
        ?.join(' ') || ''
    )
    .join(' ')
  
  const wordCount = textContent.split(/\s+/).length
  return Math.max(1, Math.ceil(wordCount / wordsPerMinute))
}

// Portable Text components for rich content rendering
const portableTextComponents = {
  types: {
    image: ({ value }: any) => (
      <div className="my-8">
        <Image
          src={urlFor(value).width(800).height(400).url()}
          alt={value.alt || 'Blog post image'}
          width={800}
          height={400}
          className="rounded-lg"
        />
        {value.caption && (
          <p className="text-sm text-muted-foreground text-center mt-2">
            {value.caption}
          </p>
        )}
      </div>
    ),
    codeBlock: ({ value }: any) => (
      <div className="my-6">
        {value.filename && (
          <div className="bg-muted px-4 py-2 text-sm font-mono border-b">
            {value.filename}
          </div>
        )}
        <pre className="bg-muted p-4 rounded-b-lg overflow-x-auto">
          <code className={`language-${value.language || 'text'}`}>
            {value.code}
          </code>
        </pre>
      </div>
    ),
  },
  marks: {
    link: ({ children, value }: any) => (
      <Link 
        href={value.href} 
        className="text-primary hover:underline"
        target={value.href.startsWith('http') ? '_blank' : undefined}
        rel={value.href.startsWith('http') ? 'noopener noreferrer' : undefined}
      >
        {children}
      </Link>
    ),
  },
  block: {
    h1: ({ children }: any) => <h1 className="text-3xl font-bold mt-8 mb-4">{children}</h1>,
    h2: ({ children }: any) => <h2 className="text-2xl font-bold mt-6 mb-3">{children}</h2>,
    h3: ({ children }: any) => <h3 className="text-xl font-bold mt-4 mb-2">{children}</h3>,
    h4: ({ children }: any) => <h4 className="text-lg font-semibold mt-4 mb-2">{children}</h4>,
    blockquote: ({ children }

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  try {
    const post = await getBlogPost(params.slug)

    if (!post) {
      notFound()
    }

    const readingTime = estimateReadingTime(post.body)

    return (
      <article className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Back button */}
        <div className="mb-6">
          <Button variant="ghost" asChild>
            <Link href="/" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to Blog
            </Link>
          </Button>
        </div>

        {/* Article header */}
        <header className="mb-8">
          {/* Categories */}
          <div className="flex flex-wrap gap-2 mb-4">
            {post.categories?.map((category) => (
              <Badge
                key={category._id}
                variant="secondary"
                className={`bg-${category.color}-100 text-${category.color}-800`}
              >
                {category.title}
              </Badge>
            ))}
          </div>

          {/* Title */}
          <h1 className="text-4xl font-bold mb-4">{post.title}</h1>

          {/* Excerpt */}
          <p className="text-xl text-muted-foreground mb-6">{post.excerpt}</p>

          {/* Meta information */}
          <div className="flex flex-wrap items-center gap-6 text-sm text-muted-foreground mb-6">
            <div className="flex items-center gap-2">
              <User className="h-4 w-4" />
              <span>{post.author.name}</span>
              <span>•</span>
              <span className="capitalize">{post.author.role}</span>
            </div>
            <div className="flex items-center gap-2">
              <CalendarDays className="h-4 w-4" />
              <span>{formatDate(post.publishedAt)}</span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              <span>{readingTime} min read</span>
            </div>
          </div>

          {/* Featured image */}
          {post.mainImage && (
            <div className="relative h-64 md:h-96 w-full overflow-hidden rounded-lg mb-8">
              <Image
                src={urlFor(post.mainImage).width(1200).height(600).url()}
                alt={post.mainImage.alt || post.title}
                fill
                className="object-cover"
                priority
              />
            </div>
          )}
        </header>

        {/* Article content */}
        <div className="prose prose-lg max-w-none">
          <PortableText value={post.body} components={portableTextComponents} />
        </div>

        {/* Tags */}
        {post.tags && post.tags.length > 0 && (
          <div className="mt-8 pt-8 border-t">
            <h3 className="text-lg font-semibold mb-3">Tags</h3>
            <div className="flex flex-wrap gap-2">
              {post.tags.map((tag) => (
                <Badge key={tag} variant="outline">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Author bio */}
        <div className="mt-8 pt-8 border-t">
          <div className="flex items-start gap-4">
            {post.author.image && (
              <Image
                src={urlFor(post.author.image).width(80).height(80).url()}
                alt={post.author.name}
                width={80}
                height={80}
                className="rounded-full"
              />
            )}
            <div>
              <h3 className="text-lg font-semibold">{post.author.name}</h3>
              <p className="text-muted-foreground capitalize mb-2">{post.author.role}</p>
              {post.author.bio && (
                <div className="prose prose-sm">
                  <PortableText value={post.author.bio} />
                </div>
              )}
              {post.author.social && (
                <div className="flex gap-4 mt-3">
                  {post.author.social.twitter && (
                    <Link
                      href={post.author.social.twitter}
                      className="text-primary hover:underline"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      Twitter
                    </Link>
                  )}
                  {post.author.social.linkedin && (
                    <Link
                      href={post.author.social.linkedin}
                      className="text-primary hover:underline"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      LinkedIn
                    </Link>
                  )}
                  {post.author.social.github && (
                    <Link
                      href={post.author.social.github}
                      className="text-primary hover:underline"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      GitHub
                    </Link>
                  )}
                  {post.author.social.website && (
                    <Link
                      href={post.author.social.website}
                      className="text-primary hover:underline"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      Website
                    </Link>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </article>
    )
  } catch (error) {
    console.error('Error loading blog post:', error)
    notFound()
  }
}: any) => (
      <blockquote className="border-l-4 border-primary pl-4 my-4 italic text-muted-foreground">
        {children}
      </blockquote>
    ),
    normal: ({ children }: any) => <p className="mb-4 leading-relaxed">{children}</p>,
  },
  list: {
    bullet: ({ children }: any) => <ul className="list-disc list-inside mb-4 space-y-1">{children}</ul>,
    number: ({ children }: any) => <ol className="list-decimal list-inside mb-4 space-y-1">{children}</ol>,
  },
}
