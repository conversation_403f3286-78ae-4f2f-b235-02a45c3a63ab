import { createClient } from '@sanity/client'
import { SanityImageSource } from '@sanity/image-url/lib/types/types'
import imageUrlBuilder from '@sanity/image-url'

// Sanity client configuration
export const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET!,
  useCdn: process.env.NODE_ENV === 'production',
  apiVersion: process.env.NEXT_PUBLIC_SANITY_API_VERSION || '2024-01-01',
  token: process.env.SANITY_API_TOKEN,
})

// Image URL builder
const builder = imageUrlBuilder(client)

export function urlFor(source: SanityImageSource) {
  return builder.image(source)
}

// TypeScript interfaces
export interface BlogPost {
  _id: string
  title: string
  slug: {
    current: string
  }
  publishedAt: string
  excerpt: string
  mainImage?: {
    asset: {
      _ref: string
    }
    alt?: string
  }
  author: {
    name: string
    image?: {
      asset: {
        _ref: string
      }
    }
    role: string
    bio?: any[]
    social?: {
      twitter?: string
      linkedin?: string
      github?: string
      website?: string
    }
  }
  categories?: {
    title: string
    slug: {
      current: string
    }
    color: string
  }[]
  tags?: string[]
  body?: any[]
  featured?: boolean
  seo?: {
    metaTitle?: string
    metaDescription?: string
    ogImage?: {
      asset: {
        _ref: string
      }
    }
  }
}

export interface Author {
  _id: string
  name: string
  slug: {
    current: string
  }
  image?: {
    asset: {
      _ref: string
    }
    alt?: string
  }
  bio?: any[]
  role: string
  social?: {
    twitter?: string
    linkedin?: string
    github?: string
    website?: string
  }
}

export interface Category {
  _id: string
  title: string
  slug: {
    current: string
  }
  description?: string
  color: string
}

// GROQ queries
export const queries = {
  // Blog queries
  allBlogPosts: `*[_type == "blogPost"] | order(publishedAt desc) {
    _id,
    title,
    slug,
    publishedAt,
    excerpt,
    mainImage,
    author->{
      name,
      image,
      role
    },
    categories[]->{
      title,
      slug,
      color
    },
    tags,
    featured
  }`,

  featuredBlogPosts: `*[_type == "blogPost" && featured == true] | order(publishedAt desc)[0...3] {
    _id,
    title,
    slug,
    publishedAt,
    excerpt,
    mainImage,
    author->{
      name,
      image,
      role
    },
    categories[]->{
      title,
      slug,
      color
    }
  }`,

  blogPostBySlug: `*[_type == "blogPost" && slug.current == $slug][0] {
    _id,
    title,
    slug,
    publishedAt,
    excerpt,
    mainImage,
    author->{
      name,
      image,
      bio,
      role,
      social
    },
    categories[]->{
      title,
      slug,
      color
    },
    tags,
    body,
    seo
  }`,

  blogPostsByCategory: `*[_type == "blogPost" && references($categoryId)] | order(publishedAt desc) {
    _id,
    title,
    slug,
    publishedAt,
    excerpt,
    mainImage,
    author->{
      name,
      image,
      role
    },
    categories[]->{
      title,
      slug,
      color
    }
  }`,

  // Author queries
  allAuthors: <AUTHORS>
    _id,
    name,
    slug,
    image,
    bio,
    role,
    social
  }`,

  authorBySlug: `*[_type == "author" && slug.current == $slug][0] {
    _id,
    name,
    slug,
    image,
    bio,
    role,
    social
  }`,

  // Category queries
  allCategories: `*[_type == "category"] | order(title asc) {
    _id,
    title,
    slug,
    description,
    color
  }`,

  categoryBySlug: `*[_type == "category" && slug.current == $slug][0] {
    _id,
    title,
    slug,
    description,
    color
  }`,
}

// Helper functions for fetching data
export async function getBlogPosts(): Promise<BlogPost[]> {
  return await client.fetch(queries.allBlogPosts)
}

export async function getFeaturedBlogPosts(): Promise<BlogPost[]> {
  return await client.fetch(queries.featuredBlogPosts)
}

export async function getBlogPost(slug: string): Promise<BlogPost | null> {
  return await client.fetch(queries.blogPostBySlug, { slug })
}

export async function getBlogPostsByCategory(categoryId: string): Promise<BlogPost[]> {
  return await client.fetch(queries.blogPostsByCategory, { categoryId })
}

export async function getAuthors(): Promise<Author[]> {
  return await client.fetch(queries.allAuthors)
}

export async function getAuthor(slug: string): Promise<Author | null> {
  return await client.fetch(queries.authorBySlug, { slug })
}

export async function getCategories(): Promise<Category[]> {
  return await client.fetch(queries.allCategories)
}

export async function getCategory(slug: string): Promise<Category | null> {
  return await client.fetch(queries.categoryBySlug, { slug })
}
