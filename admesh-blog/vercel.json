{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "installCommand": "npm install", "devCommand": "npm run dev", "env": {"NEXT_PUBLIC_SANITY_PROJECT_ID": "h0kukqbi", "NEXT_PUBLIC_SANITY_DATASET": "production", "NEXT_PUBLIC_SANITY_API_VERSION": "2024-01-01", "NEXT_PUBLIC_ENVIRONMENT": "production", "NEXT_PUBLIC_BLOG_URL": "https://blog.useadmesh.com", "NEXT_PUBLIC_MAIN_SITE_URL": "https://useadmesh.com"}, "functions": {"src/app/**": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}], "redirects": [{"source": "/blog/:path*", "destination": "/:path*", "permanent": true}]}