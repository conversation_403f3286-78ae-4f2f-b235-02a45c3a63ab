import { Metadata } from 'next'
import Link from 'next/link'
import { getBlogPosts, getDocumentation, getProducts, getAuthors, getCategories } from '@/lib/sanity'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  FileText, 
  BookOpen, 
  Package, 
  Users, 
  Tags, 
  ExternalLink, 
  Plus,
  BarChart3,
  Settings,
  Database
} from 'lucide-react'

export const metadata: Metadata = {
  title: 'CMS Admin | AdMesh Dashboard',
  description: 'Manage content, documentation, and products through Sanity CMS.',
}

function StatCard({ 
  title, 
  count, 
  description, 
  icon: Icon, 
  href 
}: { 
  title: string
  count: number
  description: string
  icon: any
  href: string
}) {
  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{count}</div>
        <p className="text-xs text-muted-foreground mb-3">{description}</p>
        <Button size="sm" variant="outline" asChild>
          <Link href={href}>
            Manage
            <ExternalLink className="h-3 w-3 ml-1" />
          </Link>
        </Button>
      </CardContent>
    </Card>
  )
}

function QuickAction({ 
  title, 
  description, 
  icon: Icon, 
  href,
  variant = "outline"
}: { 
  title: string
  description: string
  icon: any
  href: string
  variant?: "outline" | "default"
}) {
  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader>
        <div className="flex items-center gap-3">
          <Icon className="h-6 w-6 text-primary" />
          <div>
            <CardTitle className="text-lg">{title}</CardTitle>
            <CardDescription>{description}</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Button variant={variant} asChild className="w-full">
          <Link href={href} target="_blank" rel="noopener noreferrer">
            {title}
            <ExternalLink className="h-4 w-4 ml-2" />
          </Link>
        </Button>
      </CardContent>
    </Card>
  )
}

export default async function CMSAdminPage() {
  try {
    const [blogPosts, documentation, products, authors, categories] = await Promise.all([
      getBlogPosts().catch(() => []),
      getDocumentation('all').catch(() => []),
      getProducts().catch(() => []),
      getAuthors().catch(() => []),
      getCategories().catch(() => []),
    ])

    const stats = {
      blogPosts: blogPosts.length,
      documentation: documentation.length,
      products: products.length,
      authors: authors.length,
      categories: categories.length,
    }

    return (
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Content Management System</h1>
          <p className="text-muted-foreground">
            Manage all your content through Sanity CMS. Create, edit, and publish blog posts, 
            documentation, and product information.
          </p>
        </div>

        {/* Stats Overview */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5 mb-8">
          <StatCard
            title="Blog Posts"
            count={stats.blogPosts}
            description="Published articles"
            icon={FileText}
            href="http://localhost:3333/structure/blogPost"
          />
          <StatCard
            title="Documentation"
            count={stats.documentation}
            description="Help articles"
            icon={BookOpen}
            href="http://localhost:3333/structure/documentation"
          />
          <StatCard
            title="Products"
            count={stats.products}
            description="Product listings"
            icon={Package}
            href="http://localhost:3333/structure/product"
          />
          <StatCard
            title="Authors"
            count={stats.authors}
            description="Content creators"
            icon={Users}
            href="http://localhost:3333/structure/author"
          />
          <StatCard
            title="Categories"
            count={stats.categories}
            description="Content categories"
            icon={Tags}
            href="http://localhost:3333/structure/category"
          />
        </div>

        {/* Quick Actions */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold mb-4">Quick Actions</h2>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <QuickAction
              title="Open Sanity Studio"
              description="Access the full content management interface"
              icon={Database}
              href="http://localhost:3333"
              variant="default"
            />
            <QuickAction
              title="Create Blog Post"
              description="Write and publish a new blog article"
              icon={Plus}
              href="http://localhost:3333/structure/blogPost;new"
            />
            <QuickAction
              title="Add Documentation"
              description="Create new help or API documentation"
              icon={BookOpen}
              href="http://localhost:3333/structure/documentation;new"
            />
            <QuickAction
              title="Add Product"
              description="List a new product for recommendations"
              icon={Package}
              href="http://localhost:3333/structure/product;new"
            />
            <QuickAction
              title="Manage Landing Pages"
              description="Edit homepage and landing page content"
              icon={Settings}
              href="http://localhost:3333/structure/landingPageSection"
            />
            <QuickAction
              title="SEO Settings"
              description="Configure meta tags and SEO settings"
              icon={BarChart3}
              href="http://localhost:3333/structure/seoSettings"
            />
          </div>
        </div>

        {/* Content Overview */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Recent Blog Posts */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Recent Blog Posts
              </CardTitle>
              <CardDescription>
                Latest published articles
              </CardDescription>
            </CardHeader>
            <CardContent>
              {blogPosts.length > 0 ? (
                <div className="space-y-3">
                  {blogPosts.slice(0, 5).map((post) => (
                    <div key={post._id} className="flex items-center justify-between">
                      <div>
                        <Link 
                          href={`/blog/${post.slug.current}`}
                          className="font-medium hover:text-primary transition-colors"
                        >
                          {post.title}
                        </Link>
                        <p className="text-sm text-muted-foreground">
                          by {post.author.name}
                        </p>
                      </div>
                      {post.featured && (
                        <Badge variant="secondary">Featured</Badge>
                      )}
                    </div>
                  ))}
                  <Button variant="outline" size="sm" asChild className="w-full mt-4">
                    <Link href="/blog">View All Posts</Link>
                  </Button>
                </div>
              ) : (
                <div className="text-center py-6">
                  <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                  <p className="text-muted-foreground mb-3">No blog posts yet</p>
                  <Button size="sm" asChild>
                    <Link href="http://localhost:3333/structure/blogPost;new" target="_blank">
                      Create First Post
                    </Link>
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Recent Documentation */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5" />
                Recent Documentation
              </CardTitle>
              <CardDescription>
                Latest help articles and guides
              </CardDescription>
            </CardHeader>
            <CardContent>
              {documentation.length > 0 ? (
                <div className="space-y-3">
                  {documentation.slice(0, 5).map((doc) => (
                    <div key={doc._id} className="flex items-center justify-between">
                      <div>
                        <Link 
                          href={`/docs/${doc.slug.current}`}
                          className="font-medium hover:text-primary transition-colors"
                        >
                          {doc.title}
                        </Link>
                        <p className="text-sm text-muted-foreground">
                          {doc.category.replace('-', ' ')} • {doc.audience}
                        </p>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {doc.category}
                      </Badge>
                    </div>
                  ))}
                  <Button variant="outline" size="sm" asChild className="w-full mt-4">
                    <Link href="/docs">View All Docs</Link>
                  </Button>
                </div>
              ) : (
                <div className="text-center py-6">
                  <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                  <p className="text-muted-foreground mb-3">No documentation yet</p>
                  <Button size="sm" asChild>
                    <Link href="http://localhost:3333/structure/documentation;new" target="_blank">
                      Create First Doc
                    </Link>
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Help Section */}
        <div className="mt-8 p-6 bg-muted rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Need Help?</h3>
          <p className="text-muted-foreground mb-4">
            Learn how to use Sanity CMS to manage your content effectively.
          </p>
          <div className="flex flex-wrap gap-2">
            <Button variant="outline" size="sm" asChild>
              <Link href="https://www.sanity.io/docs" target="_blank" rel="noopener noreferrer">
                Sanity Documentation
                <ExternalLink className="h-3 w-3 ml-1" />
              </Link>
            </Button>
            <Button variant="outline" size="sm" asChild>
              <Link href="https://www.sanity.io/docs/portable-text" target="_blank" rel="noopener noreferrer">
                Portable Text Guide
                <ExternalLink className="h-3 w-3 ml-1" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    )
  } catch (error) {
    console.error('Error loading CMS admin data:', error)
    
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4">Content Management System</h1>
          <div className="bg-muted p-6 rounded-lg max-w-md mx-auto">
            <Database className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">CMS Setup Required</h3>
            <p className="text-muted-foreground mb-4">
              The content management system is being set up.
            </p>
            <Button asChild>
              <Link href="http://localhost:3333" target="_blank" rel="noopener noreferrer">
                Open Sanity Studio
                <ExternalLink className="h-4 w-4 ml-2" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    )
  }
}
