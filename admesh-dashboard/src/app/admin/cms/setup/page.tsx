'use client'

import { useState, useEffect } from 'react'
import { checkExistingContent, createSample<PERSON>ontent, deleteAllContent } from '@/lib/sanity-setup'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Database, 
  Plus, 
  Trash2, 
  RefreshCw, 
  CheckCircle, 
  AlertTriangle,
  ExternalLink,
  Loader2
} from 'lucide-react'

interface ContentStats {
  authors: number
  categories: number
  blogPosts: number
  documentation: number
  landingPageSections: number
  seoSettings: number
  total: number
}

export default function CMSSetupPage() {
  const [stats, setStats] = useState<ContentStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [creating, setCreating] = useState(false)
  const [deleting, setDeleting] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)

  const loadStats = async () => {
    try {
      setLoading(true)
      const contentStats = await checkExistingContent()
      setStats(contentStats)
    } catch (error) {
      console.error('Error loading stats:', error)
      setMessage({ type: 'error', text: 'Failed to load content statistics' })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadStats()
  }, [])

  const handleCreateSampleContent = async () => {
    try {
      setCreating(true)
      setMessage(null)
      await createSampleContent()
      setMessage({ type: 'success', text: 'Sample content created successfully!' })
      await loadStats()
    } catch (error) {
      console.error('Error creating sample content:', error)
      setMessage({ type: 'error', text: 'Failed to create sample content. Check console for details.' })
    } finally {
      setCreating(false)
    }
  }

  const handleDeleteAllContent = async () => {
    if (!confirm('Are you sure you want to delete ALL content? This action cannot be undone.')) {
      return
    }

    try {
      setDeleting(true)
      setMessage(null)
      await deleteAllContent()
      setMessage({ type: 'success', text: 'All content deleted successfully!' })
      await loadStats()
    } catch (error) {
      console.error('Error deleting content:', error)
      setMessage({ type: 'error', text: 'Failed to delete content. Check console for details.' })
    } finally {
      setDeleting(false)
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading content statistics...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">CMS Setup & Management</h1>
        <p className="text-muted-foreground">
          Initialize your Sanity CMS with sample content or manage existing content.
        </p>
      </div>

      {/* Message */}
      {message && (
        <Alert className={`mb-6 ${message.type === 'error' ? 'border-destructive' : 'border-green-500'}`}>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{message.text}</AlertDescription>
        </Alert>
      )}

      {/* Content Statistics */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Current Content Statistics
          </CardTitle>
          <CardDescription>
            Overview of content currently in your Sanity CMS
          </CardDescription>
        </CardHeader>
        <CardContent>
          {stats ? (
            <div className="grid gap-4 md:grid-cols-3 lg:grid-cols-6">
              <div className="text-center">
                <div className="text-2xl font-bold">{stats.authors}</div>
                <div className="text-sm text-muted-foreground">Authors</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{stats.categories}</div>
                <div className="text-sm text-muted-foreground">Categories</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{stats.blogPosts}</div>
                <div className="text-sm text-muted-foreground">Blog Posts</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{stats.documentation}</div>
                <div className="text-sm text-muted-foreground">Docs</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{stats.landingPageSections}</div>
                <div className="text-sm text-muted-foreground">Sections</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{stats.seoSettings}</div>
                <div className="text-sm text-muted-foreground">SEO</div>
              </div>
            </div>
          ) : (
            <div className="text-center py-4">
              <p className="text-muted-foreground">Unable to load statistics</p>
            </div>
          )}
          
          <div className="flex items-center justify-between mt-6 pt-6 border-t">
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">Total Documents:</span>
              <Badge variant={stats?.total === 0 ? 'secondary' : 'default'}>
                {stats?.total || 0}
              </Badge>
            </div>
            <Button variant="outline" size="sm" onClick={loadStats}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Create Sample Content */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Plus className="h-5 w-5 text-green-600" />
              Create Sample Content
            </CardTitle>
            <CardDescription>
              Populate your CMS with sample blog posts, documentation, and other content to get started quickly.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="text-sm text-muted-foreground">
                This will create:
              </div>
              <ul className="text-sm space-y-1">
                <li>• 2 sample authors</li>
                <li>• 4 content categories</li>
                <li>• 1 featured blog post</li>
                <li>• 2 documentation articles</li>
                <li>• 2 landing page sections</li>
                <li>• 3 SEO setting templates</li>
              </ul>
              
              {stats?.total === 0 ? (
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    Your CMS is empty. Creating sample content is recommended to get started.
                  </AlertDescription>
                </Alert>
              ) : (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    You already have content. Sample content will be added to existing content.
                  </AlertDescription>
                </Alert>
              )}

              <Button 
                onClick={handleCreateSampleContent} 
                disabled={creating}
                className="w-full"
              >
                {creating ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Creating Content...
                  </>
                ) : (
                  <>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Sample Content
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Manage Content */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5 text-blue-600" />
              Manage Content
            </CardTitle>
            <CardDescription>
              Access Sanity Studio to create, edit, and manage your content directly.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="text-sm text-muted-foreground">
                Sanity Studio provides a powerful interface for:
              </div>
              <ul className="text-sm space-y-1">
                <li>• Creating and editing blog posts</li>
                <li>• Managing documentation</li>
                <li>• Configuring landing pages</li>
                <li>• Setting up SEO metadata</li>
                <li>• Managing authors and categories</li>
              </ul>

              <div className="space-y-2">
                <Button asChild className="w-full">
                  <a href="http://localhost:3333" target="_blank" rel="noopener noreferrer">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Open Sanity Studio
                  </a>
                </Button>
                
                {stats && stats.total > 0 && (
                  <Button 
                    variant="destructive" 
                    onClick={handleDeleteAllContent}
                    disabled={deleting}
                    className="w-full"
                  >
                    {deleting ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Deleting All Content...
                      </>
                    ) : (
                      <>
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete All Content
                      </>
                    )}
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Help Section */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>Need Help?</CardTitle>
          <CardDescription>
            Resources to help you get the most out of your CMS setup
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <h4 className="font-semibold mb-2">Getting Started</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>1. Create sample content to populate your CMS</li>
                <li>2. Open Sanity Studio to explore the interface</li>
                <li>3. Edit the sample content to match your needs</li>
                <li>4. Create new content using the Studio</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Useful Links</h4>
              <div className="space-y-2">
                <Button variant="outline" size="sm" asChild>
                  <a href="/blog" target="_blank">
                    View Blog
                    <ExternalLink className="h-3 w-3 ml-1" />
                  </a>
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <a href="/docs" target="_blank">
                    View Documentation
                    <ExternalLink className="h-3 w-3 ml-1" />
                  </a>
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <a href="/admin/cms" target="_blank">
                    CMS Dashboard
                    <ExternalLink className="h-3 w-3 ml-1" />
                  </a>
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
