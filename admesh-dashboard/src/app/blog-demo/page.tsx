import { Metadata } from 'next'
import BlogSection from '@/components/BlogSection'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import Link from 'next/link'
import { ArrowLeft, BookOpen, Settings, ExternalLink } from 'lucide-react'

export const metadata: Metadata = {
  title: 'Blog Demo | AdMesh',
  description: 'Demonstration of the AdMesh blog system with Sanity CMS integration.',
}

function DemoControls() {
  return (
    <div className="bg-muted/50 border-b">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Home
              </Link>
            </Button>
            <Badge variant="secondary">Blog Demo</Badge>
          </div>
          
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" asChild>
              <Link href="/admin/cms/setup">
                <Settings className="h-4 w-4 mr-2" />
                Create Content
              </Link>
            </Button>
            <Button variant="outline" size="sm" asChild>
              <Link href="/blog">
                <BookOpen className="h-4 w-4 mr-2" />
                Full Blog
              </Link>
            </Button>
            <Button variant="outline" size="sm" asChild>
              <Link href="http://localhost:3333" target="_blank" rel="noopener noreferrer">
                <ExternalLink className="h-4 w-4 mr-2" />
                Sanity Studio
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

function DemoInfo() {
  return (
    <div className="bg-blue-50 border-b">
      <div className="container mx-auto px-4 py-6">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold mb-4 flex items-center gap-2">
            <BookOpen className="h-6 w-6" />
            AdMesh Blog System Demo
          </h1>
          
          <p className="text-muted-foreground mb-6">
            This page demonstrates the AdMesh blog system powered by Sanity CMS. The blog section 
            below shows how featured and regular blog posts are displayed on the landing page.
          </p>
          
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Featured Posts</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Featured blog posts are highlighted and appear first in the grid layout.
                </CardDescription>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Rich Content</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Blog posts support rich content including images, code blocks, and formatted text.
                </CardDescription>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">SEO Optimized</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Each post includes SEO metadata, Open Graph tags, and structured data.
                </CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}

function BlogFeatures() {
  const features = [
    {
      title: 'Dynamic Content',
      description: 'Blog posts are fetched from Sanity CMS and rendered dynamically',
      items: ['Real-time updates', 'Content management', 'Version control']
    },
    {
      title: 'Rich Media Support',
      description: 'Support for images, videos, code blocks, and interactive content',
      items: ['Image optimization', 'Code syntax highlighting', 'Responsive design']
    },
    {
      title: 'SEO & Performance',
      description: 'Built-in SEO optimization and performance features',
      items: ['Meta tags', 'Open Graph', 'Fast loading']
    }
  ]

  return (
    <div className="bg-white border-b">
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-2xl font-bold mb-8 text-center">Blog System Features</h2>
          
          <div className="grid gap-6 md:grid-cols-3">
            {features.map((feature, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="text-lg">{feature.title}</CardTitle>
                  <CardDescription>{feature.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {feature.items.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-center gap-2 text-sm">
                        <div className="w-1.5 h-1.5 bg-primary rounded-full" />
                        {item}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

function Instructions() {
  return (
    <div className="bg-gray-50 border-b">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <h3 className="text-xl font-semibold mb-4">How to Test the Blog System</h3>
          
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Create Sample Content</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <p className="text-sm text-muted-foreground">
                  Use the setup utility to create sample blog posts and authors.
                </p>
                <Button asChild className="w-full">
                  <Link href="/admin/cms/setup">
                    Create Sample Content
                  </Link>
                </Button>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Manage Content</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <p className="text-sm text-muted-foreground">
                  Open Sanity Studio to create and edit blog posts manually.
                </p>
                <Button variant="outline" asChild className="w-full">
                  <Link href="http://localhost:3333" target="_blank" rel="noopener noreferrer">
                    Open Sanity Studio
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </div>
          
          <div className="mt-6 p-4 bg-white rounded-lg border">
            <h4 className="font-semibold mb-2">Testing Steps</h4>
            <ol className="text-sm text-muted-foreground space-y-1 list-decimal list-inside">
              <li>Click "Create Sample Content" to populate the CMS with blog posts</li>
              <li>Refresh this page to see the blog posts appear below</li>
              <li>Visit the full blog at <Link href="/blog" className="text-primary hover:underline">/blog</Link></li>
              <li>Check individual blog posts by clicking on them</li>
              <li>View the blog section on the main landing page</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function BlogDemoPage() {
  return (
    <div className="min-h-screen">
      <DemoControls />
      <DemoInfo />
      <BlogFeatures />
      <Instructions />
      
      {/* Blog Section Demo */}
      <div className="border-t-4 border-primary">
        <div className="bg-primary/5 py-2">
          <div className="container mx-auto px-4">
            <p className="text-center text-sm text-muted-foreground">
              <strong>Live Blog Section:</strong> This is how the blog appears on the landing page
            </p>
          </div>
        </div>
        
        <BlogSection 
          title="Latest Insights from AdMesh"
          subtitle="Stay updated with the latest trends in AI agent monetization, brand marketing strategies, and platform updates"
          showFeatured={true}
          maxPosts={6}
        />
      </div>
    </div>
  )
}
