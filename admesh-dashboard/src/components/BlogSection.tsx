'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { getFeaturedBlogPosts, getBlogPosts, urlFor, type BlogPost } from '@/lib/sanity'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { CalendarDays, User, ArrowRight, BookOpen, Loader2 } from 'lucide-react'

interface BlogSectionProps {
  title?: string
  subtitle?: string
  showFeatured?: boolean
  maxPosts?: number
  className?: string
}

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  })
}

function BlogPostCard({ post, featured = false }: { post: BlogPost; featured?: boolean }) {
  return (
    <Card className={`h-full hover:shadow-lg transition-all duration-300 group ${featured ? 'md:col-span-2' : ''}`}>
      <Link href={`/blog/${post.slug.current}`}>
        {post.mainImage && (
          <div className={`relative overflow-hidden rounded-t-lg ${featured ? 'h-64' : 'h-48'}`}>
            <Image
              src={urlFor(post.mainImage).width(featured ? 600 : 400).height(featured ? 300 : 200).url()}
              alt={post.mainImage.alt || post.title}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
            />
            {post.featured && (
              <div className="absolute top-4 left-4">
                <Badge className="bg-primary text-primary-foreground">
                  Featured
                </Badge>
              </div>
            )}
          </div>
        )}
        <CardHeader>
          <div className="flex flex-wrap gap-2 mb-2">
            {post.categories?.slice(0, 2).map((category) => (
              <Badge
                key={category._id}
                variant="secondary"
                className="text-xs"
              >
                {category.title}
              </Badge>
            ))}
          </div>
          <CardTitle className={`line-clamp-2 group-hover:text-primary transition-colors ${featured ? 'text-xl' : 'text-lg'}`}>
            {post.title}
          </CardTitle>
          <CardDescription className={`line-clamp-3 ${featured ? 'text-base' : 'text-sm'}`}>
            {post.excerpt}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <User className="h-4 w-4" />
              <span>{post.author.name}</span>
            </div>
            <div className="flex items-center gap-2">
              <CalendarDays className="h-4 w-4" />
              <span>{formatDate(post.publishedAt)}</span>
            </div>
          </div>
          {post.tags && post.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-3">
              {post.tags.slice(0, 3).map((tag) => (
                <Badge key={tag} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {post.tags.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{post.tags.length - 3}
                </Badge>
              )}
            </div>
          )}
        </CardContent>
      </Link>
    </Card>
  )
}

export default function BlogSection({
  title = "Latest from Our Blog",
  subtitle = "Insights, tutorials, and updates about AI-powered recommendations and monetization",
  showFeatured = true,
  maxPosts = 6,
  className = ""
}: BlogSectionProps) {
  const [posts, setPosts] = useState<BlogPost[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchPosts() {
      try {
        setLoading(true)
        setError(null)
        
        let blogPosts: BlogPost[]
        if (showFeatured) {
          // Get featured posts first, then fill with regular posts
          const [featuredPosts, allPosts] = await Promise.all([
            getFeaturedBlogPosts(),
            getBlogPosts()
          ])
          
          // Combine featured and non-featured posts, avoiding duplicates
          const featuredIds = new Set(featuredPosts.map(post => post._id))
          const regularPosts = allPosts.filter(post => !featuredIds.has(post._id))
          
          blogPosts = [...featuredPosts, ...regularPosts].slice(0, maxPosts)
        } else {
          blogPosts = await getBlogPosts()
          blogPosts = blogPosts.slice(0, maxPosts)
        }
        
        setPosts(blogPosts)
      } catch (err) {
        console.error('Error fetching blog posts:', err)
        setError('Failed to load blog posts')
      } finally {
        setLoading(false)
      }
    }

    fetchPosts()
  }, [showFeatured, maxPosts])

  if (loading) {
    return (
      <section className={`py-20 bg-gray-50 ${className}`}>
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">{title}</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">{subtitle}</p>
          </div>
          <div className="flex items-center justify-center">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p className="text-muted-foreground">Loading blog posts...</p>
            </div>
          </div>
        </div>
      </section>
    )
  }

  if (error || posts.length === 0) {
    return (
      <section className={`py-20 bg-gray-50 ${className}`}>
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">{title}</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">{subtitle}</p>
          </div>
          <div className="text-center">
            <BookOpen className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">No blog posts available</h3>
            <p className="text-muted-foreground mb-6">
              We're working on creating amazing content for you. Check back soon!
            </p>
            <div className="bg-white p-6 rounded-lg max-w-md mx-auto border">
              <h4 className="font-semibold mb-2">Want to see sample content?</h4>
              <p className="text-sm text-muted-foreground mb-4">
                Create sample blog posts to see how this section looks with content.
              </p>
              <Button asChild>
                <Link href="/admin/cms/setup">
                  Create Sample Content
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className={`py-20 bg-gray-50 ${className}`}>
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">{title}</h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">{subtitle}</p>
        </div>

        {/* Blog Posts Grid */}
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3 mb-12">
          {posts.map((post, index) => (
            <BlogPostCard 
              key={post._id} 
              post={post} 
              featured={showFeatured && post.featured && index < 2}
            />
          ))}
        </div>

        {/* View All Button */}
        <div className="text-center">
          <Button size="lg" asChild>
            <Link href="/blog" className="inline-flex items-center gap-2">
              View All Posts
              <ArrowRight className="h-4 w-4" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  )
}
