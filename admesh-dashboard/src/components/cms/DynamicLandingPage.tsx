'use client'

import { useEffect, useState } from 'react'
import { getLandingPageSections, urlFor, type LandingPageSection } from '@/lib/sanity'
import { PortableText } from '@portabletext/react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Loader2 } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import * as LucideIcons from 'lucide-react'

interface DynamicLandingPageProps {
  page: string
  fallbackContent?: React.ReactNode
  className?: string
}

// Helper function to get Lucide icon by name
function getIcon(iconName: string) {
  const IconComponent = (LucideIcons as any)[iconName]
  return IconComponent || LucideIcons.Circle
}

// Hero Section Component
function HeroSection({ section }: { section: LandingPageSection }) {
  return (
    <section className={`py-20 ${section.backgroundColor === 'primary' ? 'bg-primary text-primary-foreground' : section.backgroundColor === 'gray-50' ? 'bg-gray-50' : 'bg-white'}`}>
      <div className="container mx-auto px-4 text-center">
        {section.backgroundImage && (
          <div className="absolute inset-0 z-0">
            <Image
              src={urlFor(section.backgroundImage).width(1920).height(1080).url()}
              alt="Background"
              fill
              className="object-cover opacity-20"
            />
          </div>
        )}
        
        <div className="relative z-10 max-w-4xl mx-auto">
          {section.headline && (
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              {section.headline}
            </h1>
          )}
          
          {section.subheadline && (
            <p className="text-xl md:text-2xl text-muted-foreground mb-8">
              {section.subheadline}
            </p>
          )}
          
          {section.content && (
            <div className="prose prose-lg mx-auto mb-8">
              <PortableText value={section.content} />
            </div>
          )}
          
          {section.ctaButtons && section.ctaButtons.length > 0 && (
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              {section.ctaButtons.map((button, index) => (
                <Button
                  key={index}
                  variant={button.style === 'primary' ? 'default' : button.style === 'secondary' ? 'secondary' : 'outline'}
                  size="lg"
                  asChild
                >
                  <Link href={button.url}>{button.text}</Link>
                </Button>
              ))}
            </div>
          )}
        </div>
      </div>
    </section>
  )
}

// Features Section Component
function FeaturesSection({ section }: { section: LandingPageSection }) {
  return (
    <section className={`py-20 ${section.backgroundColor === 'primary' ? 'bg-primary text-primary-foreground' : section.backgroundColor === 'gray-50' ? 'bg-gray-50' : 'bg-white'}`}>
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          {section.headline && (
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              {section.headline}
            </h2>
          )}
          
          {section.subheadline && (
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              {section.subheadline}
            </p>
          )}
        </div>
        
        {section.features && section.features.length > 0 && (
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {section.features.map((feature, index) => {
              const IconComponent = feature.icon ? getIcon(feature.icon) : LucideIcons.Circle
              
              return (
                <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                  <CardHeader>
                    {feature.image ? (
                      <div className="mx-auto mb-4">
                        <Image
                          src={urlFor(feature.image).width(80).height(80).url()}
                          alt={feature.title}
                          width={80}
                          height={80}
                          className="rounded-lg"
                        />
                      </div>
                    ) : (
                      <div className="mx-auto mb-4 w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center">
                        <IconComponent className="w-8 h-8 text-primary" />
                      </div>
                    )}
                    <CardTitle className="text-xl">{feature.title}</CardTitle>
                  </CardHeader>
                  {feature.description && (
                    <CardContent>
                      <CardDescription className="text-base">
                        {feature.description}
                      </CardDescription>
                    </CardContent>
                  )}
                </Card>
              )
            })}
          </div>
        )}
        
        {section.ctaButtons && section.ctaButtons.length > 0 && (
          <div className="text-center mt-12">
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              {section.ctaButtons.map((button, index) => (
                <Button
                  key={index}
                  variant={button.style === 'primary' ? 'default' : button.style === 'secondary' ? 'secondary' : 'outline'}
                  size="lg"
                  asChild
                >
                  <Link href={button.url}>{button.text}</Link>
                </Button>
              ))}
            </div>
          </div>
        )}
      </div>
    </section>
  )
}

// CTA Section Component
function CTASection({ section }: { section: LandingPageSection }) {
  return (
    <section className={`py-20 ${section.backgroundColor === 'primary' ? 'bg-primary text-primary-foreground' : section.backgroundColor === 'gray-50' ? 'bg-gray-50' : 'bg-white'}`}>
      <div className="container mx-auto px-4 text-center">
        <div className="max-w-3xl mx-auto">
          {section.headline && (
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              {section.headline}
            </h2>
          )}
          
          {section.subheadline && (
            <p className="text-xl text-muted-foreground mb-8">
              {section.subheadline}
            </p>
          )}
          
          {section.content && (
            <div className="prose prose-lg mx-auto mb-8">
              <PortableText value={section.content} />
            </div>
          )}
          
          {section.ctaButtons && section.ctaButtons.length > 0 && (
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              {section.ctaButtons.map((button, index) => (
                <Button
                  key={index}
                  variant={button.style === 'primary' ? 'default' : button.style === 'secondary' ? 'secondary' : 'outline'}
                  size="lg"
                  asChild
                >
                  <Link href={button.url}>{button.text}</Link>
                </Button>
              ))}
            </div>
          )}
        </div>
      </div>
    </section>
  )
}

// Content Section Component (generic)
function ContentSection({ section }: { section: LandingPageSection }) {
  return (
    <section className={`py-20 ${section.backgroundColor === 'primary' ? 'bg-primary text-primary-foreground' : section.backgroundColor === 'gray-50' ? 'bg-gray-50' : 'bg-white'}`}>
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          {section.headline && (
            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-center">
              {section.headline}
            </h2>
          )}
          
          {section.subheadline && (
            <p className="text-xl text-muted-foreground mb-8 text-center">
              {section.subheadline}
            </p>
          )}
          
          {section.content && (
            <div className="prose prose-lg mx-auto">
              <PortableText value={section.content} />
            </div>
          )}
        </div>
      </div>
    </section>
  )
}

// Section Renderer
function renderSection(section: LandingPageSection) {
  switch (section.sectionType) {
    case 'hero':
      return <HeroSection key={section._id} section={section} />
    case 'features':
      return <FeaturesSection key={section._id} section={section} />
    case 'cta':
      return <CTASection key={section._id} section={section} />
    case 'content':
      return <ContentSection key={section._id} section={section} />
    default:
      return <ContentSection key={section._id} section={section} />
  }
}

export default function DynamicLandingPage({ page, fallbackContent, className }: DynamicLandingPageProps) {
  const [sections, setSections] = useState<LandingPageSection[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchSections() {
      try {
        setLoading(true)
        setError(null)
        const data = await getLandingPageSections(page)
        setSections(data)
      } catch (err) {
        console.error('Error fetching landing page sections:', err)
        setError('Failed to load content')
      } finally {
        setLoading(false)
      }
    }

    fetchSections()
  }, [page])

  if (loading) {
    return (
      <div className={`flex items-center justify-center min-h-screen ${className}`}>
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Loading content...</p>
        </div>
      </div>
    )
  }

  if (error || sections.length === 0) {
    if (fallbackContent) {
      return <div className={className}>{fallbackContent}</div>
    }
    
    return (
      <div className={`container mx-auto px-4 py-20 text-center ${className}`}>
        <h2 className="text-2xl font-bold mb-4">Content Not Available</h2>
        <p className="text-muted-foreground mb-6">
          No content sections found for the "{page}" page.
        </p>
        <div className="bg-muted p-6 rounded-lg max-w-md mx-auto">
          <p className="text-sm text-muted-foreground mb-4">
            To add content, visit the Sanity Studio and create landing page sections for this page.
          </p>
          <Button asChild>
            <Link href="http://localhost:3333" target="_blank" rel="noopener noreferrer">
              Open Sanity Studio
            </Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className={className}>
      {sections.map(renderSection)}
    </div>
  )
}
