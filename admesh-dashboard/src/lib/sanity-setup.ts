import { client } from './sanity'

// Sample data for initial setup
export const sampleData = {
  authors: [
    {
      _type: 'author',
      name: '<PERSON><PERSON>',
      slug: { _type: 'slug', current: 'mani-kumar' },
      role: 'ceo',
      bio: [
        {
          _type: 'block',
          children: [
            {
              _type: 'span',
              text: 'CEO and founder of AdMesh, passionate about AI-powered recommendation systems and helping businesses monetize their digital presence.',
            },
          ],
        },
      ],
      social: {
        linkedin: 'https://linkedin.com/in/gounimanikumar12',
        github: 'https://github.com/GouniManikumar12',
        website: 'https://useadmesh.com',
      },
    },
    {
      _type: 'author',
      name: 'AdMesh Team',
      slug: { _type: 'slug', current: 'admesh-team' },
      role: 'content-writer',
      bio: [
        {
          _type: 'block',
          children: [
            {
              _type: 'span',
              text: 'The AdMesh team consists of AI researchers, software engineers, and business development experts working to revolutionize digital monetization.',
            },
          ],
        },
      ],
    },
  ],

  categories: [
    {
      _type: 'category',
      title: 'AI & Machine Learning',
      slug: { _type: 'slug', current: 'ai-machine-learning' },
      description: 'Articles about artificial intelligence and machine learning in recommendation systems',
      color: 'blue',
    },
    {
      _type: 'category',
      title: 'API Development',
      slug: { _type: 'slug', current: 'api-development' },
      description: 'Technical guides for API integration and development',
      color: 'green',
    },
    {
      _type: 'category',
      title: 'Business Strategy',
      slug: { _type: 'slug', current: 'business-strategy' },
      description: 'Monetization strategies and business insights',
      color: 'purple',
    },
    {
      _type: 'category',
      title: 'Product Updates',
      slug: { _type: 'slug', current: 'product-updates' },
      description: 'Latest features and product announcements',
      color: 'orange',
    },
  ],

  blogPosts: [
    {
      _type: 'blogPost',
      title: 'Welcome to AdMesh: Revolutionizing AI-Powered Recommendations',
      slug: { _type: 'slug', current: 'welcome-to-admesh' },
      publishedAt: new Date().toISOString(),
      excerpt: 'Discover how AdMesh is transforming the way businesses monetize their AI recommendations with our innovative platform.',
      featured: true,
      tags: ['announcement', 'ai', 'recommendations', 'monetization'],
      body: [
        {
          _type: 'block',
          children: [
            {
              _type: 'span',
              text: 'Welcome to AdMesh, the revolutionary platform that transforms AI-powered recommendations into revenue streams. In today\'s digital landscape, businesses are constantly seeking innovative ways to monetize their AI capabilities while providing value to their users.',
            },
          ],
        },
        {
          _type: 'block',
          style: 'h2',
          children: [
            {
              _type: 'span',
              text: 'The Problem We\'re Solving',
            },
          ],
        },
        {
          _type: 'block',
          children: [
            {
              _type: 'span',
              text: 'Many businesses have sophisticated AI recommendation systems but struggle to monetize them effectively. Traditional advertising models don\'t always align with user experience, and affiliate marketing can be complex to implement and manage.',
            },
          ],
        },
        {
          _type: 'block',
          style: 'h2',
          children: [
            {
              _type: 'span',
              text: 'Our Solution',
            },
          ],
        },
        {
          _type: 'block',
          children: [
            {
              _type: 'span',
              text: 'AdMesh provides a seamless integration layer that allows businesses to monetize their AI recommendations through our intelligent product matching system. Our platform connects your recommendations with relevant products and services, creating new revenue opportunities.',
            },
          ],
        },
      ],
    },
    {
      _type: 'blogPost',
      title: 'The Future of AI Agent Monetization: Why Traditional Models Are Failing',
      slug: { _type: 'slug', current: 'future-of-ai-agent-monetization' },
      publishedAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
      excerpt: 'Explore why traditional advertising models don\'t work for AI agents and how AdMesh is pioneering a new approach to monetization.',
      featured: false,
      tags: ['ai-agents', 'monetization', 'future-tech', 'business-strategy'],
      body: [
        {
          _type: 'block',
          children: [
            {
              _type: 'span',
              text: 'As AI agents become more sophisticated and ubiquitous, the question of monetization becomes increasingly critical. Traditional advertising models that work for search engines and social media platforms are proving inadequate for the conversational, context-aware nature of AI agents.',
            },
          ],
        },
        {
          _type: 'block',
          style: 'h2',
          children: [
            {
              _type: 'span',
              text: 'The Limitations of Traditional Advertising',
            },
          ],
        },
        {
          _type: 'block',
          children: [
            {
              _type: 'span',
              text: 'Banner ads, sponsored content, and keyword-based advertising interrupt the natural flow of AI conversations. Users expect personalized, contextual recommendations that feel organic to their queries, not disruptive advertisements.',
            },
          ],
        },
        {
          _type: 'block',
          style: 'h2',
          children: [
            {
              _type: 'span',
              text: 'The AdMesh Approach',
            },
          ],
        },
        {
          _type: 'block',
          children: [
            {
              _type: 'span',
              text: 'AdMesh solves this by embedding monetization directly into the recommendation engine. Instead of showing ads, AI agents provide genuinely helpful product suggestions that align with user intent, creating value for users while generating revenue for developers.',
            },
          ],
        },
      ],
    },
    {
      _type: 'blogPost',
      title: 'Building Your First AI Agent with AdMesh: A Developer\'s Guide',
      slug: { _type: 'slug', current: 'building-first-ai-agent-admesh' },
      publishedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
      excerpt: 'Step-by-step tutorial on integrating AdMesh into your AI agent to start earning revenue from day one.',
      featured: false,
      tags: ['tutorial', 'developers', 'integration', 'api'],
      body: [
        {
          _type: 'block',
          children: [
            {
              _type: 'span',
              text: 'Ready to monetize your AI agent? This comprehensive guide will walk you through integrating AdMesh into your application, from initial setup to your first revenue-generating recommendation.',
            },
          ],
        },
        {
          _type: 'block',
          style: 'h2',
          children: [
            {
              _type: 'span',
              text: 'Prerequisites',
            },
          ],
        },
        {
          _type: 'block',
          children: [
            {
              _type: 'span',
              text: 'Before we begin, make sure you have an AdMesh developer account and your API key ready. You\'ll also need a basic understanding of REST APIs and your preferred programming language.',
            },
          ],
        },
        {
          _type: 'codeBlock',
          language: 'javascript',
          filename: 'setup.js',
          code: 'import { AdMeshClient } from \'admesh-sdk\';\n\nconst client = new AdMeshClient({\n  apiKey: process.env.ADMESH_API_KEY,\n  environment: \'production\'\n});\n\n// Your first recommendation request\nconst recommendations = await client.getRecommendations({\n  query: \"best project management tools\",\n  userId: \"user123\",\n  context: {\n    industry: \"tech\",\n    teamSize: \"small\"\n  }\n});',
        },
      ],
    },
    {
      _type: 'blogPost',
      title: 'How Brands Can Reach Users at the Moment of Intent with AI Agents',
      slug: { _type: 'slug', current: 'brands-reach-users-moment-intent' },
      publishedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days ago
      excerpt: 'Learn how AdMesh helps brands connect with potential customers exactly when they\'re searching for solutions through AI agents.',
      featured: true,
      tags: ['brands', 'marketing', 'intent-based', 'customer-acquisition'],
      body: [
        {
          _type: 'block',
          children: [
            {
              _type: 'span',
              text: 'The rise of AI agents has created a new paradigm for customer acquisition. Unlike traditional search engines where users browse through multiple results, AI agents provide direct, conversational recommendations. This creates unprecedented opportunities for brands to reach users at the exact moment of intent.',
            },
          ],
        },
        {
          _type: 'block',
          style: 'h2',
          children: [
            {
              _type: 'span',
              text: 'The Power of Intent-Based Marketing',
            },
          ],
        },
        {
          _type: 'block',
          children: [
            {
              _type: 'span',
              text: 'When a user asks an AI agent \"What\'s the best CRM for a small business?\", they\'re not just browsing—they\'re actively seeking a solution. AdMesh ensures your product appears in that conversation if it matches the user\'s specific needs and context.',
            },
          ],
        },
        {
          _type: 'block',
          style: 'h2',
          children: [
            {
              _type: 'span',
              text: 'Quality Over Quantity',
            },
          ],
        },
        {
          _type: 'block',
          children: [
            {
              _type: 'span',
              text: 'AdMesh focuses on quality matches rather than broad reach. Our AI ensures that your products are only recommended when they genuinely solve the user\'s problem, leading to higher conversion rates and better customer satisfaction.',
            },
          ],
        },
      ],
    },
    {
      _type: 'blogPost',
      title: 'The Economics of AI Agent Recommendations: Revenue Sharing That Works',
      slug: { _type: 'slug', current: 'economics-ai-agent-recommendations' },
      publishedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
      excerpt: 'Deep dive into AdMesh\'s revenue sharing model and how it creates sustainable value for all participants in the ecosystem.',
      featured: false,
      tags: ['economics', 'revenue-sharing', 'business-model', 'sustainability'],
      body: [
        {
          _type: 'block',
          children: [
            {
              _type: 'span',
              text: 'Creating a sustainable ecosystem for AI agent monetization requires careful balance. AdMesh has designed a revenue sharing model that incentivizes quality recommendations while ensuring fair compensation for all participants.',
            },
          ],
        },
        {
          _type: 'block',
          style: 'h2',
          children: [
            {
              _type: 'span',
              text: 'The Three-Way Split',
            },
          ],
        },
        {
          _type: 'block',
          children: [
            {
              _type: 'span',
              text: 'Our revenue model splits earnings between users (10%), agents (60%), and AdMesh (30%). This structure ensures that agents are properly incentivized to provide quality recommendations, users are rewarded for their engagement, and AdMesh can continue improving the platform.',
            },
          ],
        },
        {
          _type: 'block',
          style: 'h2',
          children: [
            {
              _type: 'span',
              text: 'Performance-Based Rewards',
            },
          ],
        },
        {
          _type: 'block',
          children: [
            {
              _type: 'span',
              text: 'Unlike traditional advertising where brands pay for impressions or clicks, AdMesh operates on a performance basis. Brands only pay when users take meaningful actions—signups, purchases, or other verified conversions.',
            },
          ],
        },
      ],
    },
  ],

  documentation: [
    {
      _type: 'documentation',
      title: 'Getting Started with AdMesh API',
      slug: { _type: 'slug', current: 'getting-started' },
      category: 'getting-started',
      audience: 'developers',
      order: 1,
      description: 'Learn how to integrate AdMesh API into your application in just a few minutes.',
      lastUpdated: new Date().toISOString(),
      tags: ['api', 'quickstart', 'integration'],
      content: [
        {
          _type: 'block',
          children: [
            {
              _type: 'span',
              text: 'Welcome to AdMesh! This guide will help you get started with our API in just a few minutes.',
            },
          ],
        },
        {
          _type: 'block',
          style: 'h2',
          children: [
            {
              _type: 'span',
              text: 'Prerequisites',
            },
          ],
        },
        {
          _type: 'block',
          children: [
            {
              _type: 'span',
              text: 'Before you begin, make sure you have:',
            },
          ],
        },
        {
          _type: 'block',
          listItem: 'bullet',
          children: [
            {
              _type: 'span',
              text: 'An AdMesh account',
            },
          ],
        },
        {
          _type: 'block',
          listItem: 'bullet',
          children: [
            {
              _type: 'span',
              text: 'Your API key from the dashboard',
            },
          ],
        },
        {
          _type: 'block',
          listItem: 'bullet',
          children: [
            {
              _type: 'span',
              text: 'Basic knowledge of REST APIs',
            },
          ],
        },
        {
          _type: 'codeBlock',
          language: 'bash',
          filename: 'install.sh',
          code: '# Install the AdMesh SDK\nnpm install admesh-sdk\n\n# Or using Python\npip install admesh-python',
        },
      ],
    },
    {
      _type: 'documentation',
      title: 'API Authentication',
      slug: { _type: 'slug', current: 'api-authentication' },
      category: 'api-reference',
      audience: 'developers',
      order: 1,
      description: 'Learn how to authenticate your requests to the AdMesh API.',
      lastUpdated: new Date().toISOString(),
      tags: ['api', 'authentication', 'security'],
      content: [
        {
          _type: 'block',
          children: [
            {
              _type: 'span',
              text: 'All requests to the AdMesh API must be authenticated using your API key.',
            },
          ],
        },
        {
          _type: 'callout',
          type: 'warning',
          title: 'Keep your API key secure',
          content: 'Never expose your API key in client-side code or public repositories.',
        },
        {
          _type: 'codeBlock',
          language: 'javascript',
          filename: 'auth-example.js',
          code: 'const response = await fetch(\'https://api.useadmesh.com/v1/recommendations\', {\n  headers: {\n    \'Authorization\': \'Bearer YOUR_API_KEY\',\n    \'Content-Type\': \'application/json\'\n  }\n});',
        },
      ],
    },
  ],

  landingPageSections: [
    // Home page sections
    {
      _type: 'landingPageSection',
      title: 'Home Hero Section',
      sectionId: { _type: 'slug', current: 'hero' },
      page: 'home',
      sectionType: 'hero',
      order: 1,
      isActive: true,
      headline: 'Your Personal Agent.',
      subheadline: 'Don\'t just search. Discover with purpose — and earn when you show intent.',
      ctaButtons: [
        {
          text: 'Get Started',
          url: '/signup',
          style: 'primary',
        },
        {
          text: 'Learn More',
          url: '#how-it-works',
          style: 'outline',
        },
      ],
      backgroundColor: 'white',
    },

    // Users page sections
    {
      _type: 'landingPageSection',
      title: 'Users Hero Section',
      sectionId: { _type: 'slug', current: 'hero' },
      page: 'users',
      sectionType: 'hero',
      order: 1,
      isActive: true,
      headline: 'Your Personal Agent.',
      subheadline: 'Don\'t just search. Discover with purpose — and earn when you show intent.',
      ctaButtons: [
        {
          text: 'Create Your Agent',
          url: '/signup?role=user',
          style: 'primary',
        },
        {
          text: 'Learn More',
          url: '#how-it-works',
          style: 'outline',
        },
      ],
      backgroundColor: 'white',
    },
    {
      _type: 'landingPageSection',
      title: 'Users How It Works',
      sectionId: { _type: 'slug', current: 'how-it-works' },
      page: 'users',
      sectionType: 'features',
      order: 2,
      isActive: true,
      headline: 'How Your Agent Works',
      subheadline: 'Three simple steps to start earning with your personal AI agent.',
      features: [
        {
          title: 'Create Your Agent',
          description: 'Launch your personal agent instantly — unique to you.',
          icon: 'Brain',
        },
        {
          title: 'Train by Asking Queries',
          description: 'Every search you make teaches your agent what you value.',
          icon: 'Zap',
        },
        {
          title: 'Discover Offers and Rewards',
          description: 'Your agent finds products, deals, and rewards tailored to your needs.',
          icon: 'Gift',
        },
      ],
      backgroundColor: 'gray-50',
    },

    // Brands page sections
    {
      _type: 'landingPageSection',
      title: 'Brands Hero Section',
      sectionId: { _type: 'slug', current: 'hero' },
      page: 'brands',
      sectionType: 'hero',
      order: 1,
      isActive: true,
      headline: 'Reach Users the Moment They Search Through AI Agents',
      subheadline: 'AdMesh places your products directly inside real conversations happening across AI tools, right when users are searching, comparing, and ready to act.',
      ctaButtons: [
        {
          text: 'Get Started',
          url: '/signup?role=brand',
          style: 'primary',
        },
        {
          text: 'Learn More',
          url: '#how-it-works',
          style: 'outline',
        },
      ],
      backgroundColor: 'white',
    },
    {
      _type: 'landingPageSection',
      title: 'Brands How It Works',
      sectionId: { _type: 'slug', current: 'how-it-works' },
      page: 'brands',
      sectionType: 'features',
      order: 2,
      isActive: true,
      headline: 'How AdMesh Works for Brands',
      subheadline: 'Three simple steps to start reaching users at the moment of intent.',
      features: [
        {
          title: 'Publish Your Offers',
          description: 'Upload your products to the AdMesh network with structured metadata that makes them discoverable by AI agents.',
          icon: 'BrainCircuit',
        },
        {
          title: 'Get Matched by Agents',
          description: 'AI agents dynamically pull your offers based on real-time user queries and preferences.',
          icon: 'SearchCheck',
        },
        {
          title: 'Pay Only for Results',
          description: 'Only pay when users take verifiable actions — clicks, signups, or purchases. Maximize your ROI.',
          icon: 'CreditCard',
        },
      ],
      backgroundColor: 'gray-50',
    },

    // Agents page sections
    {
      _type: 'landingPageSection',
      title: 'Agents Hero Section',
      sectionId: { _type: 'slug', current: 'hero' },
      page: 'agents',
      sectionType: 'hero',
      order: 1,
      isActive: true,
      headline: 'Monetize Your AI Agent Through the AdMesh Protocol',
      subheadline: 'Integrate in minutes. Recommend verified offers. Earn when users take real action.',
      ctaButtons: [
        {
          text: 'Start Building',
          url: '/signup?role=agent',
          style: 'primary',
        },
        {
          text: 'View Documentation',
          url: '/docs',
          style: 'outline',
        },
      ],
      backgroundColor: 'white',
    },
    {
      _type: 'landingPageSection',
      title: 'Agents Integration Steps',
      sectionId: { _type: 'slug', current: 'integration' },
      page: 'agents',
      sectionType: 'features',
      order: 2,
      isActive: true,
      headline: 'Simple Integration Process',
      subheadline: 'Get your agent earning revenue in three easy steps.',
      features: [
        {
          title: 'Connect to AdMesh',
          description: 'Use our SDK or REST API to authenticate your agent and fetch live recommendations.',
          icon: 'Code',
        },
        {
          title: 'Respond with Offers',
          description: 'Get structured, intent-aligned offers and display them in chat, UI, or voice output.',
          icon: 'Zap',
        },
        {
          title: 'Track & Earn',
          description: 'Track every click and conversion. Earn based on real value delivered to users.',
          icon: 'BarChart',
        },
      ],
      backgroundColor: 'gray-50',
    },
  ],

  seoSettings: [
    {
      _type: 'seoSettings',
      title: 'Home Page SEO',
      page: 'home',
      metaTitle: 'AdMesh - AI-Powered Recommendation Monetization Platform',
      metaDescription: 'Transform your AI recommendations into revenue streams with AdMesh. Easy integration, powerful analytics, and intelligent monetization for modern businesses.',
      keywords: ['ai recommendations', 'monetization', 'api', 'revenue optimization', 'machine learning'],
      ogTitle: 'AdMesh - Monetize Your AI Recommendations',
      ogDescription: 'The leading platform for turning AI-powered recommendations into profitable revenue streams.',
      twitterCard: 'summary_large_image',
      noIndex: false,
      noFollow: false,
      lastUpdated: new Date().toISOString(),
    },
    {
      _type: 'seoSettings',
      title: 'Blog SEO',
      page: 'blog',
      metaTitle: 'AdMesh Blog - AI Recommendations & Monetization Insights',
      metaDescription: 'Latest insights, tutorials, and updates about AI-powered recommendation systems, monetization strategies, and digital commerce trends.',
      keywords: ['ai blog', 'recommendation systems', 'monetization strategies', 'api tutorials'],
      twitterCard: 'summary_large_image',
      noIndex: false,
      noFollow: false,
      lastUpdated: new Date().toISOString(),
    },
    {
      _type: 'seoSettings',
      title: 'Documentation SEO',
      page: 'docs',
      metaTitle: 'AdMesh Documentation - API Guides & Integration Help',
      metaDescription: 'Comprehensive documentation for AdMesh API, SDKs, and integration guides. Get started with AI recommendation monetization today.',
      keywords: ['api documentation', 'integration guide', 'sdk', 'developer docs'],
      twitterCard: 'summary_large_image',
      noIndex: false,
      noFollow: false,
      lastUpdated: new Date().toISOString(),
    },
  ],
}

// Function to create sample content
export async function createSampleContent() {
  try {
    console.log('Creating sample content in Sanity...')

    // Create authors first (they're referenced by blog posts)
    const authorResults = await Promise.all(
      sampleData.authors.map(author => client.create(author))
    )
    console.log(`Created ${authorResults.length} authors`)

    // Create categories
    const categoryResults = await Promise.all(
      sampleData.categories.map(category => client.create(category))
    )
    console.log(`Created ${categoryResults.length} categories`)

    // Create blog posts with author and category references
    const blogPostsWithRefs = sampleData.blogPosts.map(post => ({
      ...post,
      author: {
        _type: 'reference',
        _ref: authorResults[0]._id, // Reference first author
      },
      categories: [
        {
          _type: 'reference',
          _ref: categoryResults[0]._id, // Reference first category
        },
      ],
    }))

    const blogResults = await Promise.all(
      blogPostsWithRefs.map(post => client.create(post))
    )
    console.log(`Created ${blogResults.length} blog posts`)

    // Create documentation
    const docResults = await Promise.all(
      sampleData.documentation.map(doc => client.create(doc))
    )
    console.log(`Created ${docResults.length} documentation entries`)

    // Create landing page sections
    const sectionResults = await Promise.all(
      sampleData.landingPageSections.map(section => client.create(section))
    )
    console.log(`Created ${sectionResults.length} landing page sections`)

    // Create SEO settings
    const seoResults = await Promise.all(
      sampleData.seoSettings.map(seo => client.create(seo))
    )
    console.log(`Created ${seoResults.length} SEO settings`)

    console.log('Sample content created successfully!')
    return {
      authors: authorResults,
      categories: categoryResults,
      blogPosts: blogResults,
      documentation: docResults,
      landingPageSections: sectionResults,
      seoSettings: seoResults,
    }
  } catch (error) {
    console.error('Error creating sample content:', error)
    throw error
  }
}

// Function to check if content exists
export async function checkExistingContent() {
  try {
    const [authors, categories, blogPosts, docs, sections, seo] = await Promise.all([
      client.fetch('*[_type == "author"]'),
      client.fetch('*[_type == "category"]'),
      client.fetch('*[_type == "blogPost"]'),
      client.fetch('*[_type == "documentation"]'),
      client.fetch('*[_type == "landingPageSection"]'),
      client.fetch('*[_type == "seoSettings"]'),
    ])

    return {
      authors: authors.length,
      categories: categories.length,
      blogPosts: blogPosts.length,
      documentation: docs.length,
      landingPageSections: sections.length,
      seoSettings: seo.length,
      total: authors.length + categories.length + blogPosts.length + docs.length + sections.length + seo.length,
    }
  } catch (error) {
    console.error('Error checking existing content:', error)
    return {
      authors: 0,
      categories: 0,
      blogPosts: 0,
      documentation: 0,
      landingPageSections: 0,
      seoSettings: 0,
      total: 0,
    }
  }
}

// Function to delete all content (use with caution!)
export async function deleteAllContent() {
  try {
    console.log('Deleting all content from Sanity...')
    
    const types = ['blogPost', 'author', 'category', 'documentation', 'landingPageSection', 'seoSettings', 'product']
    
    for (const type of types) {
      const documents = await client.fetch(`*[_type == "${type}"]`)
      if (documents.length > 0) {
        await Promise.all(
          documents.map((doc: any) => client.delete(doc._id))
        )
        console.log(`Deleted ${documents.length} ${type} documents`)
      }
    }
    
    console.log('All content deleted successfully!')
  } catch (error) {
    console.error('Error deleting content:', error)
    throw error
  }
}
