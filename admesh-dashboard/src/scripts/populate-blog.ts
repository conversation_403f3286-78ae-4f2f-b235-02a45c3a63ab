import { createSampleContent } from '@/lib/sanity-setup'

async function populateBlog() {
  try {
    console.log('🚀 Starting blog content creation...')
    
    const result = await createSampleContent()
    
    console.log('✅ Blog content created successfully!')
    console.log(`📝 Created ${result.blogPosts.length} blog posts`)
    console.log(`👥 Created ${result.authors.length} authors`)
    console.log(`🏷️ Created ${result.categories.length} categories`)
    
    console.log('\n📋 Blog Posts Created:')
    result.blogPosts.forEach((post: any, index: number) => {
      console.log(`${index + 1}. ${post.title}${post.featured ? ' (Featured)' : ''}`)
    })
    
    console.log('\n🎉 You can now view the blog posts at:')
    console.log('- Blog page: http://localhost:3000/blog')
    console.log('- Landing page: http://localhost:3000 (scroll to blog section)')
    console.log('- Sanity Studio: http://localhost:3333')
    
  } catch (error) {
    console.error('❌ Error creating blog content:', error)
    process.exit(1)
  }
}

// Run the script
populateBlog()
