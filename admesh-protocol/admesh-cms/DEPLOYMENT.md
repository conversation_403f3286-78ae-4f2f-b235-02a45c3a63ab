# AdMesh Sanity Studio Deployment Guide

This guide covers deploying the AdMesh Sanity Studio to production and configuring access permissions.

## 🚀 Quick Deployment

### Prerequisites

1. **Sanity Account**: Ensure you have access to the Sanity project `h0kukqbi`
2. **Sanity CLI**: Make sure you're logged in to Sanity CLI
3. **Project Access**: Verify you have admin permissions for the project

### Check Authentication

```bash
# Check if you're logged in
sanity auth status

# Login if needed
sanity auth login
```

### Deploy Studio

```bash
# Navigate to the CMS directory
cd admesh-protocol/admesh-cms

# Build and deploy the studio
npm run deploy
```

This will deploy the studio to: `https://h0kukqbi.sanity.studio/`

## 🔧 Configuration

### Studio Configuration

The studio is configured in `sanity.config.ts`:

- **Project ID**: `h0kukqbi`
- **Dataset**: `production`
- **Title**: `AdMesh CMS`
- **Custom Structure**: Organized content sections

### Environment Variables

Ensure these are set in your production environment:

```env
NEXT_PUBLIC_SANITY_PROJECT_ID=h0kukqbi
NEXT_PUBLIC_SANITY_DATASET=production
NEXT_PUBLIC_SANITY_API_VERSION=2024-01-01
SANITY_API_TOKEN=your_production_token_here
```

## 👥 Access Management

### User Roles

Configure user access in the Sanity management console:

1. **Admin**: Full access to all content and settings
2. **Editor**: Can create, edit, and publish content
3. **Viewer**: Read-only access to content

### Adding Users

1. Go to [Sanity Management Console](https://www.sanity.io/manage)
2. Select project `h0kukqbi`
3. Navigate to "Members" tab
4. Invite users with appropriate roles

### API Tokens

For production integration:

1. Go to project settings in Sanity Management
2. Navigate to "API" tab
3. Create tokens with appropriate permissions:
   - **Read Token**: For public content fetching
   - **Write Token**: For content creation/updates (if needed)

## 🌐 Production URLs

### Studio URLs

- **Production Studio**: `https://h0kukqbi.sanity.studio/`
- **Development Studio**: `http://localhost:3333`

### API Endpoints

- **API Base**: `https://h0kukqbi.api.sanity.io/v2024-01-01/data/query/production`
- **CDN**: `https://cdn.sanity.io/`
- **Images**: `https://cdn.sanity.io/images/h0kukqbi/production/`

## 🔒 Security Configuration

### CORS Settings

Configure CORS in Sanity project settings:

1. Go to project settings → API → CORS Origins
2. Add your production domains:
   - `https://useadmesh.com`
   - `https://www.useadmesh.com`
   - `https://dashboard.useadmesh.com`
   - `http://localhost:3000` (for development)

### Content Security

- Enable **Private datasets** if needed
- Configure **Webhook secrets** for secure integrations
- Set up **API rate limiting** for production use

## 📊 Monitoring & Analytics

### Studio Analytics

Sanity provides built-in analytics for:
- Content creation and editing activity
- User access patterns
- API usage statistics

### Custom Monitoring

Consider implementing:
- Content freshness monitoring
- API response time tracking
- Error rate monitoring for CMS integrations

## 🔄 Content Workflow

### Publishing Workflow

1. **Draft**: Content is created in draft mode
2. **Review**: Content can be reviewed before publishing
3. **Publish**: Content becomes live and available via API
4. **Archive**: Old content can be archived but not deleted

### Content Validation

The schemas include validation rules for:
- Required fields
- Field length limits
- URL format validation
- Image requirements

## 🛠️ Maintenance

### Regular Tasks

1. **Content Audit**: Review and update outdated content
2. **User Access Review**: Audit user permissions quarterly
3. **Performance Monitoring**: Check API response times
4. **Backup Verification**: Ensure content backups are working

### Updates

Keep the studio updated:

```bash
# Check for updates
npm outdated

# Update Sanity packages
npm update @sanity/vision sanity

# Redeploy after updates
npm run deploy
```

## 🚨 Troubleshooting

### Common Issues

1. **Deploy Fails**
   - Check authentication: `sanity auth status`
   - Verify project permissions
   - Ensure no syntax errors in config files

2. **CORS Errors**
   - Add your domain to CORS origins in project settings
   - Check protocol (http vs https)

3. **Content Not Appearing**
   - Verify content is published (not draft)
   - Check API token permissions
   - Validate GROQ queries

4. **Studio Access Issues**
   - Check user permissions in project settings
   - Verify studio URL is correct
   - Clear browser cache and cookies

### Support Resources

- [Sanity Documentation](https://www.sanity.io/docs)
- [Sanity Community](https://www.sanity.io/community)
- [Sanity Support](https://www.sanity.io/contact/support)

## 📋 Deployment Checklist

### Pre-Deployment

- [ ] Sanity CLI authenticated
- [ ] Project permissions verified
- [ ] Content schemas tested
- [ ] Sample content created
- [ ] Environment variables configured

### Deployment

- [ ] Studio built successfully (`npm run build`)
- [ ] Studio deployed (`npm run deploy`)
- [ ] Production URL accessible
- [ ] Content appears correctly

### Post-Deployment

- [ ] User access configured
- [ ] CORS settings updated
- [ ] API tokens created
- [ ] Monitoring setup
- [ ] Team training completed

## 🔗 Integration

### Dashboard Integration

The AdMesh dashboard integrates with the deployed studio:

- Content fetching via Sanity client
- Real-time updates when content changes
- Fallback to hardcoded content if CMS unavailable
- Admin interface for content management

### API Integration

Production API calls use:
- CDN for better performance
- Proper error handling
- Caching strategies
- Rate limiting compliance

---

## Next Steps

1. **Deploy the Studio**: Run `npm run deploy` to make it live
2. **Configure Access**: Add team members with appropriate roles
3. **Create Content**: Use the setup utility or create content manually
4. **Test Integration**: Verify dashboard pulls content correctly
5. **Train Team**: Ensure content creators know how to use the studio

The AdMesh Sanity Studio is now ready for production use!
