#!/bin/bash

# AdMesh Sanity Studio Production Deployment Script
# This script handles the complete deployment process for the Sanity Studio

set -e  # Exit on any error

echo "🚀 AdMesh Sanity Studio Deployment"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "sanity.config.ts" ]; then
    print_error "sanity.config.ts not found. Please run this script from the admesh-cms directory."
    exit 1
fi

print_status "Checking Sanity CLI authentication..."

# Check if user is logged in to Sanity
if ! sanity auth status > /dev/null 2>&1; then
    print_warning "Not authenticated with Sanity CLI"
    print_status "Please log in to Sanity..."
    sanity auth login
else
    print_success "Authenticated with Sanity CLI"
fi

# Verify project access
print_status "Verifying project access..."
if ! sanity projects list | grep -q "h0kukqbi"; then
    print_error "No access to project h0kukqbi. Please check your permissions."
    exit 1
fi
print_success "Project access verified"

# Check for uncommitted changes (if in git repo)
if [ -d ".git" ]; then
    if ! git diff-index --quiet HEAD --; then
        print_warning "You have uncommitted changes. Consider committing them before deployment."
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_status "Deployment cancelled"
            exit 0
        fi
    fi
fi

# Install dependencies
print_status "Installing dependencies..."
npm ci

# Run type checking and linting
print_status "Running type checks..."
if ! npx tsc --noEmit; then
    print_error "TypeScript compilation failed"
    exit 1
fi
print_success "Type checks passed"

# Build the studio
print_status "Building Sanity Studio..."
if ! npm run build; then
    print_error "Build failed"
    exit 1
fi
print_success "Build completed successfully"

# Deploy to Sanity
print_status "Deploying to Sanity..."
if ! npm run deploy; then
    print_error "Deployment failed"
    exit 1
fi

print_success "Studio deployed successfully!"

# Get the studio URL
STUDIO_URL="https://h0kukqbi.sanity.studio/"
print_success "Studio is now available at: $STUDIO_URL"

# Optional: Open the studio in browser
read -p "Open studio in browser? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if command -v open > /dev/null; then
        open "$STUDIO_URL"
    elif command -v xdg-open > /dev/null; then
        xdg-open "$STUDIO_URL"
    else
        print_status "Please open $STUDIO_URL in your browser"
    fi
fi

# Post-deployment checks
print_status "Running post-deployment checks..."

# Check if studio is accessible
if curl -s --head "$STUDIO_URL" | head -n 1 | grep -q "200 OK"; then
    print_success "Studio is accessible at $STUDIO_URL"
else
    print_warning "Studio might not be immediately accessible. Please check in a few minutes."
fi

# Deployment summary
echo ""
echo "📋 Deployment Summary"
echo "===================="
echo "✅ Authentication verified"
echo "✅ Dependencies installed"
echo "✅ Type checks passed"
echo "✅ Build completed"
echo "✅ Studio deployed"
echo ""
echo "🔗 Studio URL: $STUDIO_URL"
echo "📚 Documentation: https://www.sanity.io/docs"
echo ""

# Next steps
echo "🎯 Next Steps:"
echo "1. Configure user access in Sanity Management Console"
echo "2. Set up CORS origins for production domains"
echo "3. Create API tokens for dashboard integration"
echo "4. Test content creation and publishing"
echo "5. Train team members on studio usage"
echo ""

print_success "Deployment completed successfully! 🎉"
