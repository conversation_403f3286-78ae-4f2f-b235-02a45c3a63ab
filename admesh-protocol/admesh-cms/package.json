{"name": "admesh", "private": true, "version": "1.0.0", "main": "package.json", "license": "UNLICENSED", "scripts": {"dev": "sanity dev", "start": "sanity start", "build": "sanity build", "deploy": "sanity deploy", "deploy-graphql": "sanity graphql deploy", "deploy-prod": "npm run build && sanity deploy --source-maps", "check": "sanity check", "dataset:export": "sanity dataset export production backup.tar.gz", "dataset:import": "sanity dataset import backup.tar.gz production", "schema:extract": "sanity schema extract", "cors:add": "sanity cors add https://useadmesh.com https://www.useadmesh.com https://dashboard.useadmesh.com", "users:list": "sanity users list", "projects:list": "sanity projects list"}, "keywords": ["sanity"], "dependencies": {"@sanity/vision": "^3.93.0", "react": "^19.1", "react-dom": "^19.1", "sanity": "^3.93.0", "styled-components": "^6.1.18"}, "devDependencies": {"@sanity/eslint-config-studio": "^5.0.2", "@types/react": "^19.1", "eslint": "^9.28", "prettier": "^3.5", "typescript": "^5.8"}, "prettier": {"semi": false, "printWidth": 100, "bracketSpacing": false, "singleQuote": true}}