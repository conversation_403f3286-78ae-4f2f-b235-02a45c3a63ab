import {defineConfig} from 'sanity'
import {structureTool} from 'sanity/structure'
import {visionTool} from '@sanity/vision'
import {schemaTypes} from './schemaTypes'

// Custom structure for better organization
const structure = (S: any) =>
  S.list()
    .title('Content')
    .items([
      // Blog section
      S.listItem()
        .title('Blog')
        .child(
          S.list()
            .title('Blog')
            .items([
              S.listItem()
                .title('Posts')
                .schemaType('blogPost')
                .child(S.documentTypeList('blogPost').title('Blog Posts')),
              S.listItem()
                .title('Authors')
                .schemaType('author')
                .child(S.documentTypeList('author').title('Authors')),
              S.listItem()
                .title('Categories')
                .schemaType('category')
                .child(S.documentTypeList('category').title('Categories')),
            ])
        ),

      // Documentation section
      S.listItem()
        .title('Documentation')
        .schemaType('documentation')
        .child(S.documentTypeList('documentation').title('Documentation')),

      // Landing pages section
      S.listItem()
        .title('Landing Pages')
        .schemaType('landingPageSection')
        .child(S.documentTypeList('landingPageSection').title('Landing Page Sections')),

      // Products section
      S.listItem()
        .title('Products')
        .schemaType('product')
        .child(S.documentTypeList('product').title('Products')),

      // Settings section
      S.listItem()
        .title('Settings')
        .child(
          S.list()
            .title('Settings')
            .items([
              S.listItem()
                .title('SEO Settings')
                .schemaType('seoSettings')
                .child(S.documentTypeList('seoSettings').title('SEO Settings')),
            ])
        ),
    ])

export default defineConfig({
  name: 'default',
  title: 'AdMesh CMS',

  projectId: 'h0kukqbi',
  dataset: 'production',

  plugins: [
    structureTool({
      structure,
    }),
    visionTool()
  ],

  schema: {
    types: schemaTypes,
  },
})
